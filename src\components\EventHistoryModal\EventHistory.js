import { EventsManager } from '@utils/APIManager';
import { capitalizeFirstLetterOfString, getTimeInDayEveningFormat } from '@utils/functions';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import moment from 'moment';
import { Calendar2CheckFill } from '@styled-icons/bootstrap/Calendar2CheckFill';
import { CalendarWeekFill } from '@styled-icons/bootstrap/CalendarWeekFill';
import { CalendarEventFill } from '@styled-icons/bootstrap/CalendarEventFill';
import { Header } from '@components/global';
import PropTypes from 'prop-types';

const TimelineContainer = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 20px;
  width: 100%;
`;

const TimelineItem = styled.div`
  display: flex;
  align-items: flex-start;
  width: 100%;
  position: relative;
  padding: 15px 0;
  gap: 15px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const TimelineOppositeContent = styled.div`
  min-width: 120px;
  text-align: right;
  color: #555;
  padding-right: 10px;
`;

const TimelineSeparator = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 35px;
`;

const Reschedule = styled(CalendarEventFill)`
  width: 28px;
  height: 28px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
`;

const Reassign = styled(CalendarWeekFill)`
  width: 28px;
  height: 28px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
`;

const Create = styled(Calendar2CheckFill)`
  width: 28px;
  height: 28px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
`;

const TimelineConnector = styled.div`
  width: 2px;
  background-color: #007bff;
  height: 40px;
  margin-top: 10px;
  position: absolute;
  top: 45px;
  left: 50%;
  transform: translateX(-50%);
`;

const TimelineContent = styled.div`
  flex: 1;
  padding-left: 10px;
`;

const EventDetails = styled.div`
  margin-top: 8px;
  font-size: 0.9em;
  color: #666;
`;

const DetailRow = styled.div`
  margin-bottom: 2px;
`;
const TimelineWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  background-color: #007bff;
  border-radius: 25px;
`;

const EventActionMap = {
  create: <Create />,
  reassign: <Reassign />,
  reschedule: <Reschedule />,
};

export const EventHistory = ({ eventId }) => {
  const [eventHistory, setEventHistory] = useState([]);

  useEffect(() => {
    const getEventHistory = async () => {
      const response = await EventsManager.getEventHistory(eventId);

      const sortedHistory =
        response?.sort((a, b) => {
          const dateA = moment(a.dateTime);
          const dateB = moment(b.dateTime);

          // Handle invalid dates
          if (!dateA.isValid() && !dateB.isValid()) return 0;
          if (!dateA.isValid()) return 1; // Put invalid dates at the end
          if (!dateB.isValid()) return -1;

          return dateA.valueOf() - dateB.valueOf();
        }) || [];

      setEventHistory(sortedHistory);
    };
    getEventHistory();
  }, [eventId]);

  const formatDateTime = (dateTime) => {
    if (!dateTime || typeof dateTime !== 'string' || Number.isNaN(Date.parse(dateTime))) {
      return 'Time not available';
    }

    return new Intl.DateTimeFormat('en-US', {
      timeZone: 'America/New_York',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    }).format(new Date(dateTime));
  };

  return (
    <TimelineContainer>
      {eventHistory?.map((history, index) => (
        <TimelineItem key={`${history?.action}-${history?.dateTime}`}>
          <TimelineOppositeContent>
            <Header h4 marginBottom={0}>
              {history?.dateTime
                ? moment(history.dateTime).format('MMM Do YYYY')
                : 'Date not available'}
            </Header>
            <Header h4 marginBottom={0}>
              {formatDateTime(history?.dateTime)}
            </Header>
          </TimelineOppositeContent>
          <TimelineSeparator>
            <TimelineWrapper>{EventActionMap[history.action]}</TimelineWrapper>
            {index !== eventHistory.length - 1 && <TimelineConnector />}
          </TimelineSeparator>
          <TimelineContent>
            <strong>{capitalizeFirstLetterOfString(history.action)}</strong>
            <div>{`${history.firstname} ${history.lastname}`}</div>
            {/* Fetching data from Event_Detail */}
            <EventDetails>
              {history.eventDetail && (
                <>
                  {history.eventDetail.date && (
                    <DetailRow>
                      <strong>Event Date:</strong>{' '}
                      {moment(history.eventDetail.date, [
                        'YYYY-MM-DD',
                        'MM/DD/YYYY',
                        moment.ISO_8601,
                      ]).format('MMM Do YYYY')}
                    </DetailRow>
                  )}
                  {history.eventDetail.startTime && (
                    <DetailRow>
                      <strong>Event Time:</strong>{' '}
                      {getTimeInDayEveningFormat(history.eventDetail.startTime)}
                      {history.eventDetail.endTime &&
                        ` - ${getTimeInDayEveningFormat(history.eventDetail.endTime)}`}
                    </DetailRow>
                  )}
                  {history.eventDetail.assignedTo && (
                    <DetailRow>
                      <strong>Assigned To:</strong> {history.eventDetail.assignedTo}
                    </DetailRow>
                  )}
                </>
              )}
            </EventDetails>
          </TimelineContent>
        </TimelineItem>
      ))}
    </TimelineContainer>
  );
};

EventHistory.propTypes = {
  eventId: PropTypes.string.isRequired,
};
