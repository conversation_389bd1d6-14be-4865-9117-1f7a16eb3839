import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const getSlots = (params) => {
  const { type, eventDuration, startEndTimes = [], sfIds, squareFeet, houseBuilt } = params;

  const hasCustomDuration = ['010003', '010005', '010006'].includes(type);

  const requiredFields = {
    'Salesforce Id': sfIds.accountId || sfIds.workOrderId,
    'Square Feet': !sfIds.workOrderId ? squareFeet : true,
    'House Built': !sfIds.workOrderId ? houseBuilt : true,
    // If it's a reschedule, calculate the duration based on start and end time on the backend
    Duration: hasCustomDuration && !startEndTimes.length ? eventDuration : true,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return [params];
};

const create = (slot) => {
  const {
    type,
    date,
    oids,
    agentName,
    startEndTimes,
    arrivalWindow,
    startTime,
    endTime,
    attributes,
    sfIds,
    notes,
    lock,
    squareFeet,
    houseBuilt,
    confirmationStatus,
    numUnit,
    gasProjectNumber,
    electricProjectNumber,
    leadSource,
    leadSourceCT,
    incomeEligibleOrMarketRate,
    company,
    sealingServiceResult,
    heaResult,
    heaCoPayWaived,
    interestedInWaitlist,
    waitlistDays,
    customerPrimaryPhoneNumber,
    customerPrimaryPhoneNumberType,
    customerSecondaryPhoneNumber,
    customerSecondaryPhoneNumberType,
    competitiveQuoteType,
    initialBlowerDoorReading,
    ductSealingOpp,
    numberOfActiveBarriers,
  } = slot;

  const requiredFields = {
    HES: oids,
    'Start Time': startTime,
    Date: date,
    'Salesforce Id': sfIds.accountId || sfIds.workOrderId,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return {
    type,
    date,
    oids,
    agentName,
    startEndTimes,
    arrivalWindow,
    startTime,
    endTime,
    attributes,
    sfIds,
    notes,
    lock,
    squareFeet,
    houseBuilt,
    confirmationStatus,
    numUnit,
    gasProjectNumber,
    electricProjectNumber,
    leadSource,
    leadSourceCT,
    incomeEligibleOrMarketRate,
    company,
    sealingServiceResult,
    heaResult,
    heaCoPayWaived,
    interestedInWaitlist,
    waitlistDays,
    customerPrimaryPhoneNumber,
    customerPrimaryPhoneNumberType,
    customerSecondaryPhoneNumber,
    customerSecondaryPhoneNumberType,
    competitiveQuoteType,
    initialBlowerDoorReading,
    ductSealingOpp,
    numberOfActiveBarriers,
  };
};

const update = (params) => {
  const {
    id,
    associatedEventIds,
    associatedEventsId,
    type,
    date,
    oids,
    startEndTimes,
    startTime,
    attributes,
    sfIds,
    notes,
    lock,
    squareFeet,
    houseBuilt,
    confirmationStatus,
    gasProjectNumber,
    electricProjectNumber,
    incomeEligibleOrMarketRate,
    leadSource,
    company,
    sealingServiceResult,
    heaResult,
    heaCoPayWaived,
    numberOfActiveBarriers,
    visitTypeChanged,
  } = params;

  const requiredFields = {
    HES: oids,
    'Start Time': startTime,
    Date: date,
    'Work Visit ID': sfIds.workVisitId,
    'Account ID': sfIds.accountId,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  const updateData = {
    id,
    associatedEventIds,
    associatedEventsId,
    date,
    oids,
    startTime,
    type,
    startEndTimes,
    attributes,
    sfIds,
    notes,
    lock,
    squareFeet,
    houseBuilt,
    confirmationStatus,
    jobLength: 1,
    gasProjectNumber,
    electricProjectNumber,
    incomeEligibleOrMarketRate,
    leadSource,
    company,
    sealingServiceResult,
    heaResult,
    heaCoPayWaived,
    numberOfActiveBarriers,
    visitTypeChanged,
  };

  return updateData;
};

const reschedule = (params) => {
  const {
    notes: { rescheduleNotes },
    date,
    startTime,
    oids,
    arrivalWindow,
  } = params;

  const requiredFields = {
    'Reschedule Reason': rescheduleNotes,
    Date: date,
    'Start Time': startTime,
    Agent: oids[0],
    'Arrival Window (Something went wrong)': arrivalWindow, // This should always be filled out by the time slot component if theyre successfully rescheduling, so something went wrong if not
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  return requiredFields;
};

const rescheduleLater = (params) => {
  const { rescheduleReason } = params;

  const requiredFields = {
    'Reschedule Reason': rescheduleReason,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);

  return requiredFields;
};

const heaInstall = { getSlots, create, update, reschedule, rescheduleLater };

export default heaInstall;
