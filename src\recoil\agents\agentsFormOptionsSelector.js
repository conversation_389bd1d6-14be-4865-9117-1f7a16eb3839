import { selector } from 'recoil';
import availableAgentsSelector from '@recoil/agents/availableAgentsSelector';
import { selectedEventState } from '@recoil/eventSidebar';
import { hasRole, isAuthorized } from '@utils/AuthUtils';

const agentFormOptionsSelector = selector({
  key: 'agentFormOptions',
  get: ({ get }) => {
    // Get all agents for the business type
    const agents = get(availableAgentsSelector);
    const selectedEvent = get(selectedEventState);

    // Check if user is HES and if this is a WX install
    const isHES = hasRole('Agent', 'HEA', 'CT') && !isAuthorized('Scheduler', 'HEA', 'CT');
    const isWXInstall = selectedEvent.type?.slice(0, 4) === '0005';
    const shouldShowGenericName = isHES && isWXInstall;

    return agents.map(({ displayName, oid, region }) => {
      // Show generic truck name for HES users scheduling WX installs
      const truckDisplayName = !shouldShowGenericName ? 'Available Truck' : displayName;
      return { key: truckDisplayName, value: oid, region };
    });
  },
});

export default agentFormOptionsSelector;
