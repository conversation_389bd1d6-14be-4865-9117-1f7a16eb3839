import React from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import styled from 'styled-components';
import { isNumeric } from '@utils/functions';
import moment from 'moment';
import { AgentImage } from '@components/global';
import StyledAgentSlot from '@components/global/StyledAgentSlot';
import { selectedEventState } from '@recoil/eventSidebar';
import { hasRole, isAuthorized } from '@utils/AuthUtils';

const AgentName = styled.div`
  margin-top: 10px;
`;

const DriveTimes = styled.div``;

const AgentSlot = ({ slot, singleAgent = false }) => {
  const [selectedSlot, setSelectedSlot] = useRecoilState(selectedEventState);

  const { date, startTime, openEnd, driveTimeTo, driveTimeFrom, oid, displayName } = slot;

  // user is HES && if this is a WX install
  const isHES = hasRole('Agent', 'HEA', 'CT') && !isAuthorized('Scheduler', 'HEA', 'CT');
  const isWXInstall = selectedSlot.type?.slice(0, 4) === '0005';

  // generic truck name for HES users scheduling WX installs
  const shouldShowGenericName = isHES && isWXInstall;
  const truckDisplayName = shouldShowGenericName ? 'Available Truck' : displayName;

  const handleSelectAgent = () => {
    let newOids = [];
    // If selected agent/crew/truck exisits, remove them
    if (selectedSlot.oids.includes(oid)) {
      newOids = selectedSlot.oids.filter((o) => o !== oid);
    } else if (singleAgent) {
      // Only one crew/truck/agent allow to do visit
      newOids = [oid];
    } else {
      // Multiple crews/trucks/agents allowed to do visit
      newOids = [...selectedSlot.oids, oid];
    }
    // Have to reset everything after the time selection (oids)

    const startEndTimes = [
      {
        start: moment(`${date} ${startTime}`),
        end: moment(`${date} ${openEnd}`),
      },
    ];

    setSelectedSlot({
      ...selectedSlot,
      oids: newOids,
      startTime,
      startEndTimes,
      endTime: openEnd,
      agentName: displayName,
    });
  };

  const selected =
    selectedSlot.date === date &&
    selectedSlot.startTime === startTime &&
    selectedSlot.oids.includes(oid);

  const hasDriveTimes = isNumeric(driveTimeTo) && isNumeric(driveTimeFrom);

  return (
    <StyledAgentSlot onClick={handleSelectAgent} selected={selected}>
      <AgentImage imageUrl={null} />
      <AgentName>{truckDisplayName}</AgentName>
      {hasDriveTimes && <DriveTimes>{driveTimeTo + driveTimeFrom} minute drive</DriveTimes>}
    </StyledAgentSlot>
  );
};

AgentSlot.propTypes = {
  slot: PropTypes.shape({
    date: PropTypes.string,
    startTime: PropTypes.string,
    oid: PropTypes.string,
    displayName: PropTypes.string,
    driveTimeTo: PropTypes.number,
    driveTimeFrom: PropTypes.number,
    openEnd: PropTypes.string,
  }).isRequired,
  singleAgent: PropTypes.bool,
};

export default AgentSlot;
