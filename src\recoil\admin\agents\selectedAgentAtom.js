import getComplexObjectRecoilState from '@recoil/utils/getComplexObjectRecoilState';

const defaultValues = {
  attributes: [],
  company: '',
  companyName: '',
  agentLead: null,
  department: '',
  departmentName: '',
  displayName: undefined, // Used for create agent, so we can display "enter agent info" instead
  email: null,
  firstname: '',
  home: null,
  lastname: '',
  manager: null,
  number: null,
  oid: '',
  phoneNumber: null,
  programs: [],
  region: '',
  regionAbbreviation: '',
  sfId: null,
  sfId2: null,
  state: '',
  homeAddress: '',
  dayStartAddress: '',
  type: 'agent',
  active: true,
  eventTypes: [],
  agentType: '',
  isManager: false,
  isReserve: false,
  travelTimeTotal: null,
  travelTime: null,
  personalEmail: '',
  sendNotification: false,
  notificationChannel: [],
};

const [selectedAgentFieldStates, selectedAgentSelector] = getComplexObjectRecoilState(
  'selectedAgentComplexState',
  defaultValues,
);

export { selectedAgentFieldStates };
export default selectedAgentSelector;
